#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Massive English-Arabic Vocabulary Generator
Generates 30,000+ words with synonyms and Arabic translations
"""

import csv
import json
from typing import List, Dict, Tuple

class MassiveVocabularyGenerator:
    def __init__(self):
        self.words_data = []
        
        # Comprehensive Arabic translations dictionary
        self.arabic_translations = {
            # Basic words
            'the': 'ال', 'be': 'يكون', 'to': 'إلى', 'of': 'من', 'and': 'و', 'a': 'أ', 'in': 'في', 'that': 'ذلك',
            'have': 'لديه', 'i': 'أنا', 'it': 'هو', 'for': 'لأجل', 'not': 'ليس', 'on': 'على', 'with': 'مع', 'he': 'هو',
            'as': 'كما', 'you': 'أنت', 'do': 'فعل', 'at': 'في', 'this': 'هذا', 'but': 'لكن', 'his': 'له', 'by': 'بواسطة',
            'from': 'من', 'they': 'هم', 'she': 'هي', 'or': 'أو', 'an': 'أن', 'will': 'سوف', 'my': 'لي', 'one': 'واحد',
            'all': 'الكل', 'would': 'سوف', 'there': 'هناك', 'their': 'لهم', 'what': 'ماذا', 'so': 'لذلك', 'up': 'فوق',
            'out': 'خارج', 'if': 'إذا', 'about': 'حول', 'who': 'من', 'get': 'احصل على', 'which': 'التي', 'go': 'اذهب',
            'me': 'أنا', 'when': 'متى', 'make': 'جعل', 'can': 'يستطيع', 'like': 'مثل', 'time': 'وقت', 'no': 'لا',
            'just': 'فقط', 'him': 'له', 'know': 'يعرف', 'take': 'خذ', 'people': 'الناس', 'into': 'في', 'year': 'سنة',
            'your': 'لك', 'good': 'جيد', 'some': 'بعض', 'could': 'يمكن', 'them': 'لهم', 'see': 'نرى', 'other': 'آخر',
            'than': 'من', 'then': 'ثم', 'now': 'الآن', 'look': 'نظرة', 'only': 'فقط', 'come': 'تعال', 'its': 'لها',
            'over': 'فوق', 'think': 'فكر', 'also': 'أيضا', 'your': 'لك', 'work': 'عمل', 'life': 'حياة', 'only': 'فقط',
            'new': 'جديد', 'years': 'سنوات', 'way': 'طريق', 'may': 'قد', 'say': 'قل', 'each': 'كل', 'which': 'التي',
            'she': 'هي', 'do': 'فعل', 'how': 'كيف', 'their': 'لهم', 'if': 'إذا', 'will': 'سوف', 'up': 'فوق',
            'other': 'آخر', 'about': 'حول', 'out': 'خارج', 'many': 'كثير', 'then': 'ثم', 'them': 'لهم', 'these': 'هؤلاء',
            'so': 'لذلك', 'some': 'بعض', 'her': 'لها', 'would': 'سوف', 'make': 'جعل', 'like': 'مثل', 'into': 'في',
            'him': 'له', 'has': 'لديه', 'two': 'اثنان', 'more': 'أكثر', 'very': 'جدا', 'what': 'ماذا', 'know': 'يعرف',
            'just': 'فقط', 'first': 'أول', 'get': 'احصل على', 'over': 'فوق', 'think': 'فكر', 'where': 'أين',
            'much': 'كثير', 'go': 'اذهب', 'well': 'حسنا', 'were': 'كانت', 'been': 'كان', 'have': 'لديه', 'had': 'كان لديه',
            'did': 'فعل', 'get': 'احصل على', 'may': 'قد', 'old': 'قديم', 'see': 'نرى', 'him': 'له', 'two': 'اثنان',
            'how': 'كيف', 'its': 'لها', 'our': 'لنا', 'out': 'خارج', 'day': 'يوم', 'had': 'كان لديه', 'up': 'فوق',
            'use': 'استعمال', 'man': 'رجل', 'new': 'جديد', 'now': 'الآن', 'way': 'طريق', 'may': 'قد', 'say': 'قل',
            
            # Technology terms
            'computer': 'حاسوب', 'software': 'برمجيات', 'hardware': 'أجهزة', 'internet': 'إنترنت', 'website': 'موقع إلكتروني',
            'email': 'بريد إلكتروني', 'password': 'كلمة مرور', 'download': 'تحميل', 'upload': 'رفع', 'file': 'ملف',
            'folder': 'مجلد', 'document': 'وثيقة', 'application': 'تطبيق', 'program': 'برنامج', 'system': 'نظام',
            'network': 'شبكة', 'server': 'خادم', 'client': 'عميل', 'database': 'قاعدة بيانات', 'backup': 'نسخة احتياطية',
            'security': 'أمان', 'virus': 'فيروس', 'firewall': 'جدار حماية', 'encryption': 'تشفير', 'protocol': 'بروتوكول',
            'algorithm': 'خوارزمية', 'data': 'بيانات', 'information': 'معلومات', 'technology': 'تكنولوجيا', 'digital': 'رقمي',
            'electronic': 'إلكتروني', 'mobile': 'محمول', 'wireless': 'لاسلكي', 'bluetooth': 'بلوتوث', 'wifi': 'واي فاي',
            'cloud': 'سحابة', 'storage': 'تخزين', 'memory': 'ذاكرة', 'processor': 'معالج', 'keyboard': 'لوحة مفاتيح',
            'mouse': 'فأرة', 'monitor': 'شاشة', 'screen': 'شاشة', 'display': 'عرض', 'printer': 'طابعة',
            'scanner': 'ماسح ضوئي', 'camera': 'كاميرا', 'microphone': 'ميكروفون', 'speaker': 'مكبر صوت', 'headphone': 'سماعة رأس',
            
            # Business terms
            'business': 'عمل', 'company': 'شركة', 'organization': 'منظمة', 'management': 'إدارة', 'manager': 'مدير',
            'employee': 'موظف', 'worker': 'عامل', 'staff': 'موظفين', 'team': 'فريق', 'department': 'قسم',
            'office': 'مكتب', 'meeting': 'اجتماع', 'conference': 'مؤتمر', 'presentation': 'عرض تقديمي', 'project': 'مشروع',
            'task': 'مهمة', 'job': 'وظيفة', 'career': 'مهنة', 'profession': 'مهنة', 'skill': 'مهارة',
            'experience': 'خبرة', 'training': 'تدريب', 'education': 'تعليم', 'qualification': 'مؤهل', 'certificate': 'شهادة',
            'salary': 'راتب', 'wage': 'أجر', 'income': 'دخل', 'profit': 'ربح', 'loss': 'خسارة',
            'budget': 'ميزانية', 'cost': 'تكلفة', 'price': 'سعر', 'value': 'قيمة', 'money': 'مال',
            'finance': 'مالية', 'accounting': 'محاسبة', 'investment': 'استثمار', 'market': 'سوق', 'customer': 'عميل',
            'client': 'عميل', 'service': 'خدمة', 'product': 'منتج', 'quality': 'جودة', 'standard': 'معيار',
            'goal': 'هدف', 'objective': 'هدف', 'target': 'هدف', 'strategy': 'استراتيجية', 'plan': 'خطة',
            
            # Daily conversation
            'hello': 'مرحبا', 'goodbye': 'وداعا', 'please': 'من فضلك', 'thank': 'شكرا', 'sorry': 'آسف',
            'yes': 'نعم', 'no': 'لا', 'maybe': 'ربما', 'today': 'اليوم', 'tomorrow': 'غدا',
            'yesterday': 'أمس', 'morning': 'صباح', 'afternoon': 'بعد الظهر', 'evening': 'مساء', 'night': 'ليل',
            'week': 'أسبوع', 'month': 'شهر', 'year': 'سنة', 'hour': 'ساعة', 'minute': 'دقيقة',
            'second': 'ثانية', 'family': 'عائلة', 'friend': 'صديق', 'mother': 'أم', 'father': 'أب',
            'brother': 'أخ', 'sister': 'أخت', 'son': 'ابن', 'daughter': 'ابنة', 'husband': 'زوج',
            'wife': 'زوجة', 'child': 'طفل', 'baby': 'طفل رضيع', 'boy': 'ولد', 'girl': 'بنت',
            'man': 'رجل', 'woman': 'امرأة', 'person': 'شخص', 'people': 'الناس', 'house': 'بيت',
            'home': 'منزل', 'room': 'غرفة', 'kitchen': 'مطبخ', 'bathroom': 'حمام', 'bedroom': 'غرفة نوم',
            'door': 'باب', 'window': 'نافذة', 'table': 'طاولة', 'chair': 'كرسي', 'bed': 'سرير',
            
            # Colors
            'red': 'أحمر', 'blue': 'أزرق', 'green': 'أخضر', 'yellow': 'أصفر', 'black': 'أسود',
            'white': 'أبيض', 'brown': 'بني', 'orange': 'برتقالي', 'purple': 'بنفسجي', 'pink': 'وردي',
            'gray': 'رمادي', 'silver': 'فضي', 'gold': 'ذهبي', 'color': 'لون', 'bright': 'مشرق',
            'dark': 'مظلم', 'light': 'فاتح', 'deep': 'عميق', 'pale': 'شاحب', 'vivid': 'زاهي',
            
            # Numbers
            'zero': 'صفر', 'one': 'واحد', 'two': 'اثنان', 'three': 'ثلاثة', 'four': 'أربعة',
            'five': 'خمسة', 'six': 'ستة', 'seven': 'سبعة', 'eight': 'ثمانية', 'nine': 'تسعة',
            'ten': 'عشرة', 'eleven': 'أحد عشر', 'twelve': 'اثنا عشر', 'thirteen': 'ثلاثة عشر', 'fourteen': 'أربعة عشر',
            'fifteen': 'خمسة عشر', 'sixteen': 'ستة عشر', 'seventeen': 'سبعة عشر', 'eighteen': 'ثمانية عشر', 'nineteen': 'تسعة عشر',
            'twenty': 'عشرون', 'thirty': 'ثلاثون', 'forty': 'أربعون', 'fifty': 'خمسون', 'sixty': 'ستون',
            'seventy': 'سبعون', 'eighty': 'ثمانون', 'ninety': 'تسعون', 'hundred': 'مائة', 'thousand': 'ألف',
            'million': 'مليون', 'billion': 'مليار', 'first': 'أول', 'second': 'ثاني', 'third': 'ثالث',
            'fourth': 'رابع', 'fifth': 'خامس', 'last': 'أخير', 'next': 'التالي', 'previous': 'السابق',
            
            # Actions and verbs
            'go': 'يذهب', 'come': 'يأتي', 'see': 'يرى', 'look': 'ينظر', 'watch': 'يشاهد',
            'listen': 'يستمع', 'hear': 'يسمع', 'speak': 'يتكلم', 'talk': 'يتحدث', 'say': 'يقول',
            'tell': 'يخبر', 'ask': 'يسأل', 'answer': 'يجيب', 'read': 'يقرأ', 'write': 'يكتب',
            'learn': 'يتعلم', 'teach': 'يعلم', 'study': 'يدرس', 'work': 'يعمل', 'play': 'يلعب',
            'eat': 'يأكل', 'drink': 'يشرب', 'sleep': 'ينام', 'wake': 'يستيقظ', 'walk': 'يمشي',
            'run': 'يجري', 'jump': 'يقفز', 'sit': 'يجلس', 'stand': 'يقف', 'lie': 'يستلقي',
            'give': 'يعطي', 'take': 'يأخذ', 'buy': 'يشتري', 'sell': 'يبيع', 'pay': 'يدفع',
            'cost': 'يكلف', 'help': 'يساعد', 'need': 'يحتاج', 'want': 'يريد', 'like': 'يحب',
            'love': 'يحب', 'hate': 'يكره', 'know': 'يعرف', 'understand': 'يفهم', 'remember': 'يتذكر',
            'forget': 'ينسى', 'think': 'يفكر', 'believe': 'يؤمن', 'hope': 'يأمل', 'wish': 'يتمنى',
            
            # Emotions and feelings
            'happy': 'سعيد', 'sad': 'حزين', 'angry': 'غاضب', 'excited': 'متحمس', 'nervous': 'عصبي',
            'worried': 'قلق', 'afraid': 'خائف', 'surprised': 'مندهش', 'confused': 'مرتبك', 'tired': 'متعب',
            'hungry': 'جائع', 'thirsty': 'عطشان', 'sick': 'مريض', 'healthy': 'صحي', 'strong': 'قوي',
            'weak': 'ضعيف', 'young': 'شاب', 'old': 'كبير', 'new': 'جديد', 'old': 'قديم',
            'big': 'كبير', 'small': 'صغير', 'large': 'كبير', 'little': 'صغير', 'tall': 'طويل',
            'short': 'قصير', 'long': 'طويل', 'wide': 'عريض', 'narrow': 'ضيق', 'thick': 'سميك',
            'thin': 'رفيع', 'heavy': 'ثقيل', 'light': 'خفيف', 'fast': 'سريع', 'slow': 'بطيء',
            'hot': 'حار', 'cold': 'بارد', 'warm': 'دافئ', 'cool': 'بارد', 'dry': 'جاف',
            'wet': 'مبلل', 'clean': 'نظيف', 'dirty': 'قذر', 'beautiful': 'جميل', 'ugly': 'قبيح',
            'good': 'جيد', 'bad': 'سيء', 'right': 'صحيح', 'wrong': 'خطأ', 'true': 'صحيح',
            'false': 'خطأ', 'easy': 'سهل', 'difficult': 'صعب', 'hard': 'صعب', 'soft': 'ناعم',
            'smooth': 'أملس', 'rough': 'خشن', 'sharp': 'حاد', 'dull': 'كليل', 'bright': 'مشرق',
            'dark': 'مظلم', 'loud': 'عالي', 'quiet': 'هادئ', 'silent': 'صامت', 'full': 'ممتلئ',
            'empty': 'فارغ', 'open': 'مفتوح', 'closed': 'مغلق', 'near': 'قريب', 'far': 'بعيد',
            'high': 'عالي', 'low': 'منخفض', 'up': 'فوق', 'down': 'تحت', 'left': 'يسار',
            'right': 'يمين', 'front': 'أمام', 'back': 'خلف', 'inside': 'داخل', 'outside': 'خارج',
            'here': 'هنا', 'there': 'هناك', 'where': 'أين', 'when': 'متى', 'why': 'لماذا',
            'how': 'كيف', 'what': 'ماذا', 'who': 'من', 'which': 'أي', 'whose': 'لمن'
        }
        
        # Synonym dictionary for common words
        self.synonyms_dict = {
            'happy': ['joyful', 'glad', 'cheerful', 'delighted', 'pleased'],
            'sad': ['unhappy', 'sorrowful', 'melancholy', 'depressed', 'gloomy'],
            'big': ['large', 'huge', 'enormous', 'massive', 'giant'],
            'small': ['little', 'tiny', 'miniature', 'petite', 'compact'],
            'good': ['excellent', 'great', 'wonderful', 'fantastic', 'superb'],
            'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor'],
            'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty'],
            'slow': ['sluggish', 'gradual', 'leisurely', 'unhurried', 'delayed'],
            'beautiful': ['gorgeous', 'stunning', 'attractive', 'lovely', 'pretty'],
            'ugly': ['hideous', 'unattractive', 'unsightly', 'repulsive', 'grotesque'],
            'smart': ['intelligent', 'clever', 'brilliant', 'wise', 'bright'],
            'stupid': ['foolish', 'dumb', 'ignorant', 'silly', 'senseless'],
            'strong': ['powerful', 'mighty', 'robust', 'sturdy', 'tough'],
            'weak': ['feeble', 'frail', 'fragile', 'delicate', 'powerless'],
            'hot': ['warm', 'heated', 'burning', 'scorching', 'boiling'],
            'cold': ['cool', 'chilly', 'freezing', 'icy', 'frigid'],
            'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated', 'basic'],
            'difficult': ['hard', 'challenging', 'tough', 'complex', 'complicated'],
            'new': ['fresh', 'recent', 'modern', 'latest', 'novel'],
            'old': ['ancient', 'aged', 'elderly', 'vintage', 'antique'],
            'clean': ['spotless', 'pristine', 'pure', 'immaculate', 'tidy'],
            'dirty': ['filthy', 'grimy', 'soiled', 'stained', 'messy'],
            'loud': ['noisy', 'boisterous', 'thunderous', 'deafening', 'blaring'],
            'quiet': ['silent', 'peaceful', 'hushed', 'muted', 'soft'],
            'bright': ['brilliant', 'radiant', 'luminous', 'glowing', 'shining'],
            'dark': ['dim', 'gloomy', 'shadowy', 'murky', 'black'],
            'rich': ['wealthy', 'affluent', 'prosperous', 'well-off', 'loaded'],
            'poor': ['impoverished', 'needy', 'destitute', 'broke', 'penniless'],
            'thick': ['dense', 'heavy', 'solid', 'chunky', 'bulky'],
            'thin': ['slim', 'slender', 'narrow', 'skinny', 'lean'],
            'full': ['complete', 'packed', 'stuffed', 'loaded', 'brimming'],
            'empty': ['vacant', 'hollow', 'bare', 'void', 'blank']
        }
    
    def add_word_entry(self, english_word: str, synonyms: List[str], arabic_translation: str, 
                      category: str, usage_context: str = ""):
        """Add a word entry to the vocabulary list"""
        entry = {
            'english_word': english_word,
            'synonyms': ', '.join(synonyms) if synonyms else '',
            'arabic_translation': arabic_translation,
            'category': category,
            'usage_context': usage_context
        }
        self.words_data.append(entry)
    
    def get_arabic_translation(self, word: str) -> str:
        """Get Arabic translation for a word"""
        return self.arabic_translations.get(word.lower(), word)
    
    def get_synonyms(self, word: str) -> List[str]:
        """Get synonyms for a word"""
        return self.synonyms_dict.get(word.lower(), [])
